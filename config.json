{"accounts": [{"amount_usdt": 20, "api_key": "${BYBIT_API_KEY}", "api_secret": "${BYBIT_API_SECRET}", "direction": "long", "enabled": true, "exchange": "bybit", "leverage": 20, "name": "suenot: bybit", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -**********, "tp": [10], "trailing_enabled": true, "trailing_percent": 10}, {"amount_usdt": 20, "api_key": "${BINGX_API_KEY}", "api_secret": "${BINGX_API_SECRET}", "direction": "long", "enabled": true, "exchange": "bingx", "leverage": 20, "name": "suenot: bingx", "sl": [10], "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_enabled": true, "telegram_logs_chat": -**********, "tp": [10, 20, 30], "trailing_enabled": true, "trailing_percent": 20}], "proxies": ["socks5:80.91.211.26:63969:VZMp5PKM:53BYByBB", "socks5:77.90.177.22:63595:VZMp5PKM:53BYByBB", "socks5:193.9.20.170:62281:VZMp5PKM:53BYByBB", "socks5:193.200.199.218:62165:VZMp5PKM:53BYByBB", "socks5:***********:62833:VZMp5PKM:53BYByBB", "socks5:*************:63637:VZMp5PKM:53BYByBB", "socks5:**************:63853:VZMp5PKM:53BYByBB", "socks5:**************:64931:VZMp5PKM:53BYByBB", "socks5:**************:64817:VZMp5PKM:53BYByBB", "socks5:**************:64577:VZMp5PKM:53BYByBB", "socks5:***************:63255:VZMp5PKM:53BYByBB", "socks5:**************:63789:VZMp5PKM:53BYByBB"], "settings": {"api": {"playground": "dev", "urls": {"dev": {"announcements_api": "http://localhost:8080/api/v1/announcements", "markets_api": "http://localhost:8080/v1/market/all", "service_center": "http://localhost:8080/service_center/notice"}, "prod": {"announcements_api": "https://api-manager.upbit.com/api/v1/announcements", "markets_api": "https://api.upbit.com/v1/market/all", "service_center": "https://upbit.com/service_center/notice"}}}, "cycle": true, "interval_tuning": {"check_period_minutes": 60, "enabled": true, "max_interval_ms": 60000, "max_rollback_count": 10, "min_interval_ms": 1000, "tune_step_ms": 100}, "announcements_api": {"cycle_interval_ms": 4800, "enabled": false, "max_attempts": 8, "max_retry_after_sec": 100, "page_delay_jitter_ms": 100, "page_delay_ms": 1000, "proxy": true}, "markets_api": {"enabled": false, "interval_ms": 4800, "max_attempts": 5, "proxy": true, "timeout_sec": 30}, "service_center": {"enabled": false, "interval_ms": 4800, "max_attempts": 3, "proxy": true, "timeout_sec": 30}, "trade": true}, "telegram_signals": {"enabled": true, "telegram_bot_token": "${TELEGRAM_BOT_TOKEN}", "telegram_signals_chat": -4952846534}}