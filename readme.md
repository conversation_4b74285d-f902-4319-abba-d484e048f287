# Upbit Listing Monitor

Мониторинг новых листингов на Upbit с автоматической торговлей на Bybit/BingX.

## Быстрый старт

### Конфигурация через config.json

- `config.json` уже лежит в корне и подхватывается автоматически
- Запуск: `go run .` или `./upbit-listing`
- Все настройки задаются в конфигурационном файле

### Структура конфигурации

```json
{
  "settings": {
    "cycle": true,
    "trade": true,
    "news": {
      "enabled": true,
      "cycle_interval_ms": 4000,
      "max_attempts": 8,
      "max_retry_after_sec": 100,
      "page_delay_ms": 1000,
      "page_delay_jitter_ms": 100,
      "proxy": true
    },
    "pairs_diff": {
      "enabled": true,
      "interval_ms": 1000,
      "max_attempts": 5,
      "timeout_sec": 15,
      "proxy": true
    }
  },
  "accounts": [
    {
      "enabled": true,
      "exchange": "bybit",
      "name": "Account Name",
      "api_key": "${BYBIT_API_KEY}",
      "api_secret": "${BYBIT_API_SECRET}",
      "direction": "long",
      "amount_usdt": 50,
      "leverage": 10,
      "tp": [10.0],
      "sl": [10.0]
    }
  ]
}
```

### Настройки (settings)

- **cycle**: запуск в циклическом режиме
- **trade**: включить автоматическую торговлю
- **news**: настройки для парсинга новостей
  - **enabled**: включить парсинг новостей (true/false)
  - **cycle_interval_ms**: пауза между итерациями в миллисекундах
  - **max_attempts**: максимальное число повторов запросов
  - **max_retry_after_sec**: верхний предел ожидания при ошибках
  - **page_delay_ms**: базовая пауза между запросами страниц
  - **page_delay_jitter_ms**: джиттер к базовой паузе
  - **proxy**: использовать прокси для парсинга новостей (true/false)
- **pairs_diff**: настройки для отслеживания торговых пар
  - **enabled**: включить отслеживание изменений пар (true/false)
  - **interval_ms**: интервал проверки в миллисекундах
  - **max_attempts**: максимальное число повторов запросов
  - **timeout_sec**: таймаут запроса в секундах
  - **proxy**: использовать прокси для отслеживания пар (true/false)

### Управление источниками

Источники данных теперь управляются через настройки `enabled` в каждой секции:
- Если `news.enabled: true` - включен парсинг новостей с веб-сайта
- Если `pairs_diff.enabled: true` - включено отслеживание изменений торговых пар
- Если ни один источник не включен, по умолчанию активируется `news`

### Аккаунты (accounts)

Каждый аккаунт содержит:
- **exchange**: биржа (`bybit`, `bingx`)
- **direction**: направление (`long`, `short`)
- **amount_usdt**: сумма позиции в USDT
- **leverage**: кредитное плечо
- **tp**: тейк-профиты в процентах
- **sl**: стоп-лоссы в процентах

### Переменные окружения

Требуемые переменные (хранятся в `.env`):
- `BYBIT_API_KEY`, `BYBIT_API_SECRET`
- `BINGX_API_KEY`, `BINGX_API_SECRET`
- `TELEGRAM_BOT_TOKEN` (опционально)

### Прокси

Прокси настраиваются в секции `proxies` конфигурационного файла и могут использоваться как для `news`, так и для `pairs_diff` циклов.

**Формат прокси:**
```json
"proxies": [
  "http:186.179.63.18:9595:username:password",
```

**Использование прокси:**

- **news.proxy: true** - запускает независимые циклы для каждого прокси + direct для парсинга новостей
- **pairs_diff.proxy: true** - запускает независимые циклы для каждого прокси + direct для отслеживания торговых пар
- **Оба true** - оба цикла используют все доступные прокси параллельно
- **Оба false** - используются только прямые соединения
  "http:*************:9692:username:password"
]
```

**Формат**: `scheme:ip:port:user:pass`
- **scheme**: тип прокси (поддерживается только `http`)
- **ip**: IP-адрес прокси-сервера
- **port**: порт прокси-сервера
- **user**: имя пользователя для аутентификации
- **pass**: пароль для аутентификации

**Примечание**: Все прокси в конфигурации должны быть HTTP прокси. Поддержка HTTPS и SOCKS5 не реализована.

## Дополнительные команды

### Пере-проверка поддерживаемых бирж
```bash
go run ./cmd/recheck-exchanges
```

### Мониторинг торговых пар Upbit (API)
```bash
go run ./cmd/watch-upbit-markets
```

### Симулятор торговли
```bash
go run ./cmd/simulate-trade -symbol=ETH -direction=long -amount-usdt=50 -leverage=3 -tp=2,4 -sl=1
```

## Особенности

- **Jitter**: случайная добавка к базовой задержке для разсинхронизации запросов
- **Прокси**: поддержка HTTP прокси для обхода ограничений (формат: `http:ip:port:user:pass`)
- **Telegram**: уведомления о новых листингах и логи торговли
- **Автоматическая торговля**: открытие позиций при обнаружении новых листингов
- **Множественные TP/SL**: поддержка нескольких уровней тейк-профита и стоп-лосса

## Архитектура

Система построена на foundation-first подходе:
1. **Конфигурация**: все настройки в JSON файле
2. **Модульность**: отдельные команды для разных задач
3. **Надежность**: retry логика, прокси, jitter
4. **Мониторинг**: логирование и Telegram уведомления